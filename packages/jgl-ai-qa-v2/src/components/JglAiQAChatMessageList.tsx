import { JglImage, JglText, JglTouchable, JglYStack } from '@jgl/ui-v4';
import { useAtom, useAtomValue } from 'jotai';
import {
  forwardRef,
  Fragment,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Keyboard,
  type NativeScrollEvent,
  type NativeSyntheticEvent,
  Platform,
} from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { useThrottledCallback } from 'use-debounce';
import {
  jglAiQAChatMessageListAutoScrollAtom,
  jglAiQAChatMessageListBackToBottomBtnVisibleAtom,
  jglAiQAInputToolBarEditBarVisibleAtom,
  jglAiQAInputToolBarHeightAtom,
} from '../atoms/jglAiQAAtoms';
import { useIMMessages } from '../hooks/useIMCoreLogic';
import type { IMMessage } from '../store/IMMessage';
import { JglAiQAChatMessageListItem } from './JglAiQAChatMessageListItem';

type Props = {
  sessionId: string;
  /** 是否正在加载更多历史消息 */
  isLoadingMore: boolean;
  /** 是否可以加载更多历史消息 */
  canLoadingMore: boolean;
  /** 底部安全区域高度 */
  bottomSafeAreaHeight: number;
  /** 重新接收失败的AI回答消息按钮点击事件 */
  onPressResendInMessage: (message: IMMessage) => Promise<void>;
  /** 重新发送失败的用户提问消息按钮点击事件 */
  onPressResendOutMessage: (param: { message: IMMessage }) => Promise<void>;
  /** 复制消息内容按钮点击事件 */
  onPressCopy: (message: IMMessage) => void;
  /** 播放AI回答语音按钮点击事件 */
  onPressPlayAIMessage: (message: IMMessage) => void;
  /** 不喜欢按钮点击事件 */
  onPressDislike: (message: IMMessage) => void;
  /** 重新生成按钮点击事件 */
  onPressRegenerate: (message: IMMessage) => void;
  /** 加载更多老的历史消息事件 */
  onLoadMoreOldMsgs: () => void;
  /** 点击了追问消息 */
  onPressSendMessage: (param: { textContent: string }) => void;
  /** 删除消息按钮点击事件 */
  onPressDeleteMessage?: (message: IMMessage) => Promise<void>;
  /** 清空消息按钮点击事件 */
  onPressClearSession?: (message: IMMessage) => Promise<void>;
  renderHeader?: React.ReactElement;
};

/**
 * <AUTHOR>
 * @description AI 问答聊天列表
 * @date 2025_04_23
 */
export const JglAiQAChatMessageList = forwardRef<FlatList<IMMessage>, Props>(
  (props, ref) => {
    const {
      sessionId,
      isLoadingMore,
      canLoadingMore,
      bottomSafeAreaHeight,
      onPressResendInMessage,
      onPressResendOutMessage,
      onPressCopy,
      onPressPlayAIMessage,
      onPressDislike,
      onPressRegenerate,
      onLoadMoreOldMsgs,
      onPressSendMessage,
      onPressDeleteMessage,
      onPressClearSession,
      renderHeader,
    } = props;
    const messagesFromHooks = useIMMessages({ sessionId });
    console.log(
      '🚀🚀🚀🚀🚀🚀 - leejunhui ~ messagesFromHooks:',
      messagesFromHooks,
    );
    const messages = useMemo(() => {
      let msgList =
        messagesFromHooks?.filter((msg: IMMessage) => {
          return !msg.isHidden && !msg.isDeleted;
        }) ?? [];

      msgList = msgList.filter((msg) => !msg.isHidden);
      return msgList;
    }, [messagesFromHooks]);

    // const keyBoardHeight = useAppSelector((state) => state.keyBoardHeight);
    const inputToolBarHeight = useAtomValue(jglAiQAInputToolBarHeightAtom);

    const scrollViewRef = useRef<FlatList<IMMessage>>(null);
    const [autoScroll, setAutoScroll] = useAtom(
      jglAiQAChatMessageListAutoScrollAtom,
    );
    const autoScrollRef = useRef<boolean>(autoScroll);
    useEffect(() => {
      autoScrollRef.current = autoScroll;
    }, [autoScroll]);

    // const historyMessages = useAtomValue(jglAiQAChatMessageListHistoryMessagesAtom);
    const [backToBottomBtnVisible, setBackToBottomBtnVisible] = useAtom(
      jglAiQAChatMessageListBackToBottomBtnVisibleAtom,
    );
    const contentHeightRef = useRef(0);
    const [keyboardHeight, setKeyboardHeight] = useState(0);

    useEffect(() => {
      const showSubscription = Keyboard.addListener(
        Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
        (e) => {
          setKeyboardHeight(e.endCoordinates.height);
        },
      );
      const hideSubscription = Keyboard.addListener(
        Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
        () => {
          setKeyboardHeight(0);
        },
      );

      return () => {
        showSubscription.remove();
        hideSubscription.remove();
      };
    }, []);

    const onScrollToBottom = useCallback(() => {
      setBackToBottomBtnVisible(false);
      // console.log('JglAiQAChatMessageList - onScrollToBottom 来了', contentHeightRef.current);
      scrollViewRef.current?.scrollToOffset({
        offset: Math.max(0, contentHeightRef.current),
        animated: true,
      });
    }, [setBackToBottomBtnVisible]);

    // 监听消息列表变化，自动滚动到底部
    useEffect(() => {
      // console.log('JglAiQAChatMessageList - messages 变化了，自动滚动到底部', autoScroll);
      if (autoScroll && messages.length > 0) {
        setTimeout(() => {
          onScrollToBottom();
        }, 300);
      }
    }, [messages, autoScroll, onScrollToBottom]);

    // 监听键盘高度变化，自动滚动到底部
    useEffect(() => {
      if (keyboardHeight > 0) {
        // 键盘弹起时有动画，延迟滚动以确保滚动到正确位置
        setTimeout(() => {
          if (messages.length > 0) {
            onScrollToBottom();
          }
        }, 300);
      }
    }, [keyboardHeight, messages, onScrollToBottom]);

    // 用户触摸滚动时，暂停自动滚动
    const handleTouchStart = useCallback(() => {
      Keyboard.dismiss();
      // console.log('JglAiQAChatMessageList - handleTouchStart 来了，关闭 autoScroll');
      if (autoScrollRef.current) {
        setAutoScroll(false);
      }
    }, [setAutoScroll]);

    const handleScrollToTop = useCallback(() => {
      console.log(
        'JglAiQAChatMessageList - handleScrollToTop 来了，加载更多历史消息',
      );
      const capableLoadingMoreMsgs =
        !isLoadingMore && canLoadingMore && messages.length > 0;
      console.log(
        'JglAiQAChatMessageList - handleScrollToTop 来了，isLoadingMore',
        isLoadingMore,
      );
      console.log(
        'JglAiQAChatMessageList - handleScrollToTop canLoadingMore',
        canLoadingMore,
      );
      console.log(
        'JglAiQAChatMessageList - handleScrollToTop messages.length > 0',
        messages.length > 0,
      );
      console.log(
        'JglAiQAChatMessageList - handleScrollToTop 来了，capableLoadingMoreMsgs',
        capableLoadingMoreMsgs,
      );
      if (capableLoadingMoreMsgs) {
        onLoadMoreOldMsgs();
      }
    }, [canLoadingMore, isLoadingMore, messages.length, onLoadMoreOldMsgs]);

    const jglAiQAInputToolBarEditBarVisible = useAtomValue(
      jglAiQAInputToolBarEditBarVisibleAtom,
    );

    const handleScroll = useCallback(
      (e: NativeSyntheticEvent<NativeScrollEvent>) => {
        // console.log('JglAiQAChatMessageList - handleScroll 来了', e);
        // @ts-ignore
        const { contentOffset, contentSize, layoutMeasurement } = e.nativeEvent;
        const { y: scrollTop } = contentOffset;
        const { height: scrollHeight } = contentSize;
        const { height: viewportHeight } = layoutMeasurement;
        // 计算到达底部的阈值
        const reachToBottomThreshold = scrollHeight - viewportHeight;
        const delta =
          inputToolBarHeight +
          (jglAiQAInputToolBarEditBarVisible ? 34 : 0) +
          16;

        // console.log('📊 滚动数据 ===> ', {
        //   scrollHeight,
        //   scrollTop,
        //   viewportHeight,
        //   reachToBottomThreshold,
        //   显示回到底部按钮: scrollTop + delta < reachToBottomThreshold,
        // });

        if (scrollTop + delta < reachToBottomThreshold) {
          // console.log('📜 哎呀，用户正在探索历史消息的海洋...');
          // console.log(`🔍 当前滚动深度: ${scrollTop}, 总高度: ${scrollHeight}`);
          // console.log('⬆️ 显示回到底部按钮，帮助用户快速返航~');
          setBackToBottomBtnVisible(true);
        } else {
          // console.log('🎯 用户已到达聊天的最新区域，隐藏回到底部按钮');
          setBackToBottomBtnVisible(false);
        }
      },
      [
        inputToolBarHeight,
        jglAiQAInputToolBarEditBarVisible,
        setBackToBottomBtnVisible,
      ],
    );

    const throttleHandleScroll = useThrottledCallback(handleScroll, 100, {
      leading: true,
      trailing: true,
    });

    const renderItem = useCallback(
      ({ item }: { item: IMMessage }) => {
        return (
          <JglAiQAChatMessageListItem
            key={item.id}
            message={item}
            isLastMessage={item.id === messages[messages.length - 1]?.id}
            onPressResendInMessage={onPressResendInMessage}
            onPressResendOutMessage={onPressResendOutMessage}
            onPressCopy={onPressCopy}
            onPressPlayAIMessage={onPressPlayAIMessage}
            onPressDislike={onPressDislike}
            onPressRegenerate={onPressRegenerate}
            onPressSendMessage={onPressSendMessage}
            onPressDeleteMessage={onPressDeleteMessage}
            onPressClearSession={onPressClearSession}
          />
        );
      },
      [
        messages,
        onPressClearSession,
        onPressCopy,
        onPressDeleteMessage,
        onPressDislike,
        onPressPlayAIMessage,
        onPressRegenerate,
        onPressResendInMessage,
        onPressResendOutMessage,
        onPressSendMessage,
      ],
    );

    const handleContentSizeChange = useCallback((w: number, h: number) => {
      contentHeightRef.current = h;
    }, []);

    const handleMomentumScrollEnd = useCallback(
      (event: NativeSyntheticEvent<NativeScrollEvent>) => {
        const { contentOffset, contentSize, layoutMeasurement } =
          event.nativeEvent;
        console.log(
          'handleMomentumScrollEnd',
          contentOffset,
          contentSize,
          layoutMeasurement,
        );
        if (contentOffset.y === 0) {
          handleScrollToTop();
        }
      },
      [handleScrollToTop],
    );

    const containerPaddingBottom = useMemo(() => {
      return inputToolBarHeight + (jglAiQAInputToolBarEditBarVisible ? 34 : 0);
    }, [inputToolBarHeight, jglAiQAInputToolBarEditBarVisible]);

    const renderListHeaderComponent = useCallback(() => {
      return (
        <JglYStack py={6} space={12}>
          {messages.length > 0 ? (
            <JglText color='#AEC0F5' fontSize={12} alignSelf='center'>
              内容由AI生成，仅供参考
            </JglText>
          ) : null}
        </JglYStack>
      );
    }, [messages.length]);

    return (
      <Fragment>
        {renderHeader}

        <FlatList
          ref={scrollViewRef}
          data={messages}
          renderItem={renderItem}
          keyExtractor={(item) => item.id?.toString() ?? ''}
          bounces={true}
          onContentSizeChange={handleContentSizeChange}
          scrollEventThrottle={16}
          onTouchStart={handleTouchStart}
          onMomentumScrollEnd={handleMomentumScrollEnd}
          // onEndReached={handleScrollToBottom}
          onScroll={handleScroll}
          // estimatedItemSize={100}
          style={{
            // backgroundColor: 'red',
            marginBottom: containerPaddingBottom,
          }}
          ListHeaderComponent={renderListHeaderComponent}
          // stickyHeaderIndices={[0]}
        />

        {/* 回到底部按钮 */}
        <JglTouchable
          position='absolute'
          bottom={
            inputToolBarHeight +
            (jglAiQAInputToolBarEditBarVisible ? 34 : 0) +
            10
          }
          w={36}
          h={36}
          borderRadius={9999}
          bg='white'
          style={{
            // boxShadow: '0 0 15 0 #17171740',
            alignSelf: 'center',
            // visibility: backToBottomBtnVisible ? 'visible' : 'hidden',
            // transition: 'all 0.3s linear',
            shadowColor: '#171717',
            shadowOffset: { width: 0, height: 0 },
            shadowOpacity: 0.25,
            shadowRadius: 15,
            elevation: 10,
            opacity: backToBottomBtnVisible ? 1 : 0,
          }}
          // @ts-ignore
          pointerEvents={backToBottomBtnVisible ? 'auto' : 'none'}
          onPress={onScrollToBottom}
        >
          <JglImage
            source={require('../assets/images/ic_ai_chat_scroll_to_bottom.png')}
            w={17}
            h={17}
          />
        </JglTouchable>
      </Fragment>
    );
  },
);
